package com.example.coloringproject.manager

import android.graphics.Color
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.example.coloringproject.data.ColorPalette
import com.example.coloringproject.data.Region
import com.example.coloringproject.model.ColorInfo
import com.example.coloringproject.view.ColoringView

/**
 * 填色状态管理器
 * 负责管理填色状态、颜色选择、进度计算等核心填色逻辑
 */
class ColoringStateManager {
    
    private val TAG = "ColoringStateManager"
    
    // 当前状态
    var currentColoringData: ColoringData? = null
        private set
    var currentSelectedColor: ColorPalette? = null
        private set
    val filledRegions = mutableSetOf<Int>()
    
    // 缓存处理后的调色板
    private var cachedProcessedPalette: List<ColorPalette> = emptyList()
    
    // 回调接口
    interface StateChangeListener {
        fun onColorSelected(color: ColorPalette)
        fun onRegionFilled(regionId: Int)
        fun onProgressUpdated(filled: Int, total: Int)
        fun onColorCompleted(colorHex: String, regionCount: Int)
        fun onProjectCompleted()
        fun onPaletteUpdated(filteredPalette: List<ColorPalette>)
    }
    
    private var listener: StateChangeListener? = null
    
    fun setStateChangeListener(listener: StateChangeListener) {
        this.listener = listener
    }
    
    /**
     * 初始化项目数据
     */
    fun initializeProject(coloringData: ColoringData) {
        this.currentColoringData = coloringData
        filledRegions.clear()
        cachedProcessedPalette = processColorPalette(coloringData.colorPalette, coloringData.regions)
        
        // 选择第一个颜色
        if (cachedProcessedPalette.isNotEmpty()) {
            selectColor(cachedProcessedPalette.first())
        }
        
        Log.d(TAG, "项目初始化完成: ${coloringData.metadata.totalRegions}个区域")
    }
    
    /**
     * 快速初始化（用于进度恢复）
     */
    fun initializeProjectFast(coloringData: ColoringData, savedFilledRegions: Set<Int>) {
        Log.d(TAG, "=== ColoringStateManager快速初始化 ===")
        Log.d(TAG, "接收到的ColoringData:")
        Log.d(TAG, "  - 区域总数: ${coloringData.regions.size}")
        Log.d(TAG, "  - 调色板大小: ${coloringData.colorPalette.size}")
        Log.d(TAG, "  - 元数据: ${coloringData.metadata}")
        Log.d(TAG, "保存的填色区域: ${savedFilledRegions.size}")
        
        this.currentColoringData = coloringData
        filledRegions.clear()
        
        // 验证填色区域的有效性
        val validRegionIds = coloringData.regions.map { it.id }.toSet()
        val validFilledRegions = savedFilledRegions.filter { validRegionIds.contains(it) }.toSet()
        val invalidRegions = savedFilledRegions - validFilledRegions
        
        Log.d(TAG, "区域ID验证结果:")
        Log.d(TAG, "  - 项目中的前10个区域ID: ${validRegionIds.take(10)}")
        Log.d(TAG, "  - 保存的区域ID: $savedFilledRegions")
        Log.d(TAG, "  - 有效的区域ID: $validFilledRegions")
        Log.d(TAG, "  - 无效的区域ID: $invalidRegions")
        
        if (invalidRegions.isNotEmpty()) {
            Log.w(TAG, "发现${invalidRegions.size}个无效的填色区域ID: $invalidRegions")
        }
        
        // 只添加有效的填色区域
        filledRegions.addAll(validFilledRegions)
        
        // 使用完整的调色板处理以便正确计算进度
        cachedProcessedPalette = processColorPalette(coloringData.colorPalette, coloringData.regions)
        Log.d(TAG, "处理后的调色板: ${cachedProcessedPalette.size}个颜色")
        
        // 选择第一个未完成的颜色
        val firstUnfinishedColor = findFirstUnfinishedColor()
        if (firstUnfinishedColor != null) {
            selectColor(firstUnfinishedColor)
            Log.d(TAG, "选择第一个未完成的颜色: ${firstUnfinishedColor.name}")
        } else if (cachedProcessedPalette.isNotEmpty()) {
            selectColor(cachedProcessedPalette.first())
            Log.d(TAG, "所有颜色已完成，选择第一个颜色: ${cachedProcessedPalette.first().name}")
        } else {
            Log.w(TAG, "警告：没有可用的颜色")
        }
        
        // 通知调色板更新（过滤掉已完成的颜色）
        val filteredPalette = getFilteredPalette()
        listener?.onPaletteUpdated(filteredPalette)

        // 输出详细的进度信息
        val totalProgress = getTotalProgress()
        Log.d(TAG, "=== 快速初始化完成 ===")
        Log.d(TAG, "有效填色区域: ${validFilledRegions.size}/${savedFilledRegions.size}")
        Log.d(TAG, "总进度: ${totalProgress.first}/${totalProgress.second}")
        Log.d(TAG, "进度百分比: ${if (totalProgress.second > 0) (totalProgress.first * 100 / totalProgress.second) else 0}%")
        Log.d(TAG, "过滤后调色板: ${filteredPalette.size}个颜色")
    }
    
    /**
     * 选择颜色
     */
    fun selectColor(color: ColorPalette) {
        currentSelectedColor = color
        Log.d(TAG, "ColoringStateManager选择颜色: ${color.name} (${color.colorHex})")
        Log.d(TAG, "触发listener回调: ${listener != null}")
        listener?.onColorSelected(color)
    }
    
    /**
     * 根据颜色十六进制值自动选择颜色
     */
    fun autoSelectColorByHex(colorHex: String): Boolean {
        val normalizedColorHex = normalizeColorHex(colorHex)
        
        // 检查是否是背景色
        if (isBackgroundColor(normalizedColorHex)) {
            return false
        }
        
        val matchingColor = cachedProcessedPalette.find { palette ->
            normalizeColorHex(palette.colorHex) == normalizedColorHex
        }
        
        return if (matchingColor != null) {
            selectColor(matchingColor)
            true
        } else {
            false
        }
    }
    
    /**
     * 处理区域填色
     */
    fun fillRegion(regionId: Int): Boolean {
        val data = currentColoringData ?: return false
        
        if (filledRegions.add(regionId)) {
            listener?.onRegionFilled(regionId)
            
            // 更新进度
            val totalRegions = data.metadata.totalRegions
            listener?.onProgressUpdated(filledRegions.size, totalRegions)
            
            // 检查颜色是否完成
            checkColorCompletion(regionId)
            
            // 检查项目是否完成
            if (filledRegions.size >= totalRegions) {
                listener?.onProjectCompleted()
            }
            
            return true
        }
        return false
    }
    
    /**
     * 获取当前颜色进度
     */
    fun getCurrentColorProgress(): Pair<Int, Int> {
        val currentColor = currentSelectedColor ?: return Pair(0, 0)
        val data = currentColoringData ?: return Pair(0, 0)
        
        val normalizedColorHex = normalizeColorHex(currentColor.colorHex)
        val colorRegions = data.regions.filter { region ->
            normalizeColorHex(region.colorHex) == normalizedColorHex
        }
        
        val filledCount = colorRegions.count { region ->
            filledRegions.contains(region.id)
        }
        
        return Pair(filledCount, colorRegions.size)
    }
    
    /**
     * 获取总体进度
     */
    fun getTotalProgress(): Pair<Int, Int> {
        val data = currentColoringData ?: return Pair(0, 0)
        return Pair(filledRegions.size, data.metadata.totalRegions)
    }
    
    /**
     * 获取处理后的调色板
     */
    fun getProcessedPalette(): List<ColorPalette> = cachedProcessedPalette

    /**
     * 获取过滤掉已完成颜色的调色板（用于显示）
     */
    fun getFilteredPalette(): List<ColorPalette> {
        return cachedProcessedPalette.filter { !it.isCompleted }
    }

    /**
     * 获取完整的调色板（包含已完成的颜色，用于编号映射）
     */
    fun getCompletePalette(): List<ColorPalette> = cachedProcessedPalette
    
    /**
     * 转换为ColorInfo列表
     */
    fun getColorInfoList(): List<ColorInfo> {
        return cachedProcessedPalette.mapIndexed { index, palette ->
            ColorInfo(
                id = index + 1, // 从1开始编号
                name = palette.name,
                hexColor = palette.colorHex
            )
        }
    }

    /**
     * 转换为过滤后的ColorInfo列表（移除已完成的颜色）
     */
    fun getFilteredColorInfoList(): List<ColorInfo> {
        return getFilteredPalette().mapIndexed { index, palette ->
            ColorInfo(
                id = palette.id, // 使用原始ID保持编号固定
                name = palette.name,
                hexColor = palette.colorHex
            )
        }
    }
    
    /**
     * 重置项目
     */
    fun resetProject() {
        filledRegions.clear()
        val data = currentColoringData ?: return
        listener?.onProgressUpdated(0, data.metadata.totalRegions)
    }
    
    /**
     * 恢复进度
     */
    fun restoreProgress(savedFilledRegions: Set<Int>): Boolean {
        val data = currentColoringData ?: return false
        
        // 验证区域ID的有效性
        val validRegionIds = data.regions.map { it.id }.toSet()
        val validSavedRegions = savedFilledRegions.filter { validRegionIds.contains(it) }.toSet()
        
        if (validSavedRegions.size != savedFilledRegions.size) {
            Log.w(TAG, "部分保存的区域ID无效，已过滤")
        }
        
        filledRegions.clear()
        filledRegions.addAll(validSavedRegions)
        
        // 重新处理调色板以更新完成状态
        cachedProcessedPalette = processColorPalette(data.colorPalette, data.regions)

        // 通知调色板更新（过滤掉已完成的颜色）
        val filteredPalette = getFilteredPalette()
        listener?.onPaletteUpdated(filteredPalette)

        // 更新进度
        listener?.onProgressUpdated(filledRegions.size, data.metadata.totalRegions)

        Log.d(TAG, "进度恢复完成: ${validSavedRegions.size}个区域，过滤后调色板: ${filteredPalette.size}个颜色")
        return true
    }
    
    // 私有方法
    
    private fun processColorPalette(originalPalette: List<ColorPalette>, regions: List<Region>): List<ColorPalette> {
        val regionsByColor = regions.groupBy { normalizeColorHex(it.colorHex) }
        val uniqueColors = mutableMapOf<String, ColorPalette>()
        
        for (palette in originalPalette) {
            val normalizedColorHex = normalizeColorHex(palette.colorHex)
            
            if (!uniqueColors.containsKey(normalizedColorHex)) {
                val colorRegions = regionsByColor[normalizedColorHex] ?: emptyList()
                val totalCount = colorRegions.size
                val filledCount = colorRegions.count { filledRegions.contains(it.id) }
                
                uniqueColors[normalizedColorHex] = palette.copy(
                    filledCount = filledCount,
                    totalCount = totalCount
                )
            }
        }
        
        return uniqueColors.values.toList().sortedBy { it.id }
    }
    
    private fun processColorPaletteSimple(originalPalette: List<ColorPalette>): List<ColorPalette> {
        return originalPalette.map { palette ->
            palette.copy(filledCount = 0, totalCount = 0)
        }
    }
    
    private fun findFirstUnfinishedColor(): ColorPalette? {
        val data = currentColoringData ?: return null
        
        return cachedProcessedPalette.find { palette ->
            val normalizedColorHex = normalizeColorHex(palette.colorHex)
            val colorRegions = data.regions.filter { region ->
                normalizeColorHex(region.colorHex) == normalizedColorHex
            }
            val filledCount = colorRegions.count { region ->
                filledRegions.contains(region.id)
            }
            filledCount < colorRegions.size
        }
    }
    
    private fun checkColorCompletion(regionId: Int) {
        val data = currentColoringData ?: return
        val region = data.regions.find { it.id == regionId } ?: return
        
        val normalizedColorHex = normalizeColorHex(region.colorHex)
        val colorRegions = data.regions.filter { 
            normalizeColorHex(it.colorHex) == normalizedColorHex 
        }
        
        val filledCount = colorRegions.count { filledRegions.contains(it.id) }
        
        if (filledCount == colorRegions.size) {
            listener?.onColorCompleted(region.colorHex, colorRegions.size)

            // 重新处理调色板以更新完成状态
            val data = currentColoringData ?: return
            cachedProcessedPalette = processColorPalette(data.colorPalette, data.regions)

            // 通知调色板更新（过滤掉已完成的颜色）
            val filteredPalette = getFilteredPalette()
            listener?.onPaletteUpdated(filteredPalette)

            Log.d(TAG, "颜色完成后更新调色板: ${filteredPalette.size}个剩余颜色")
        }
    }
    
    private fun normalizeColorHex(colorHex: String): String {
        var normalized = colorHex.trim().lowercase()
        if (!normalized.startsWith("#")) {
            normalized = "#$normalized"
        }
        if (normalized.length == 4) {
            val r = normalized[1]
            val g = normalized[2]
            val b = normalized[3]
            normalized = "#$r$r$g$g$b$b"
        }
        return normalized
    }
    
    private fun isBackgroundColor(normalizedColorHex: String): Boolean {
        val backgroundColors = setOf(
            "#ffffff", "#000000", "#00ffffff", "#00000000", 
            "#ffffffff", "#ff000000"
        )
        return backgroundColors.contains(normalizedColorHex.lowercase())
    }
}