package com.example.coloringproject.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import com.example.coloringproject.data.ColoringData
import com.google.gson.Gson
import com.google.gson.JsonParser
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException

/**
 * 简化的资源管理器，支持优化格式
 */
class SimpleAssetManager(private val context: Context) {

    private val gson = Gson()

    /**
     * 加载填色数据（支持优化和传统格式）
     */
    suspend fun loadColoringData(jsonFileName: String): Result<ColoringData> = withContext(Dispatchers.IO) {
        try {
            Log.d("SimpleAssetManager", "Loading JSON file: $jsonFileName")
            val inputStream = context.assets.open(jsonFileName)
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            Log.d("SimpleAssetManager", "JSON string length: ${jsonString.length}")

            // 检查JSON字符串是否为空
            if (jsonString.isBlank()) {
                throw IllegalArgumentException("JSON file is empty: $jsonFileName")
            }

            // 解析JSON并检查格式
            val jsonObject = JsonParser.parseString(jsonString).asJsonObject

            // 检查是否为超级压缩格式
            val isUltraCompressed = jsonObject.has("meta") && jsonObject.has("regions") && jsonObject.has("palette")
            val metadata = jsonObject.getAsJsonObject("metadata")
            val isOptimized = metadata?.has("compression") == true

            Log.d("SimpleAssetManager", "File format: ${when {
                isUltraCompressed -> "Ultra Compressed"
                isOptimized -> "Optimized"
                else -> "Legacy"
            }}")

            val coloringData = when {
                isUltraCompressed -> {
                    // 处理超级压缩格式
                    convertUltraCompressedToColoringData(jsonObject)
                }
                isOptimized -> {
                    // 处理优化格式
                    convertOptimizedJsonToColoringData(jsonObject)
                }
                else -> {
                    // 处理传统格式
                    gson.fromJson(jsonString, ColoringData::class.java)
                }
            }
            
            // 验证解析结果
            if (coloringData == null) {
                throw IllegalArgumentException("Failed to parse JSON: $jsonFileName")
            }

            // 验证必要字段
            if (coloringData.metadata == null) {
                throw IllegalArgumentException("Missing metadata in JSON: $jsonFileName")
            }

            if (coloringData.regions.isEmpty()) {
                throw IllegalArgumentException("No regions found in JSON: $jsonFileName")
            }

            if (coloringData.colorPalette.isEmpty()) {
                throw IllegalArgumentException("No color palette found in JSON: $jsonFileName")
            }

            Log.d("SimpleAssetManager", "Successfully parsed JSON: ${coloringData.regions.size} regions, ${coloringData.colorPalette.size} colors")
            Result.success(coloringData)
            
        } catch (e: Exception) {
            Log.e("SimpleAssetManager", "Error loading coloring data", e)
            Result.failure(e)
        }
    }

    /**
     * 转换优化格式的JSON到ColoringData
     */
    private fun convertOptimizedJsonToColoringData(jsonObject: com.google.gson.JsonObject): ColoringData {
        // 处理区域数据
        val regionsArray = jsonObject.getAsJsonArray("regions")
        val regions = regionsArray.map { regionElement ->
            val regionJson = regionElement.asJsonObject
            
            // 获取像素数据（支持压缩格式）
            val pixels = if (regionJson.has("pixels_compressed")) {
                decompressPixels(regionJson.getAsJsonObject("pixels_compressed"))
            } else if (regionJson.has("pixels")) {
                // 回退到传统格式
                regionJson.getAsJsonArray("pixels").map { pixel ->
                    val coords = pixel.asJsonArray
                    listOf(coords[0].asInt, coords[1].asInt)
                }
            } else {
                emptyList()
            }
            
            // 创建Region对象
            com.example.coloringproject.data.Region(
                id = regionJson.get("id").asInt,
                clusterId = regionJson.get("cluster_id")?.asInt,
                color = regionJson.get("color")?.asJsonArray?.map { it.asInt } ?: listOf(255, 255, 255),
                colorHex = regionJson.get("color_hex").asString,
                area = regionJson.get("area")?.asInt ?: pixels.size,
                pixelCount = regionJson.get("pixel_count")?.asInt ?: pixels.size,
                pixels = pixels,
                boundingBox = regionJson.get("bounding_box")?.asJsonArray?.map { it.asInt },
                fillOrder = regionJson.get("fill_order")?.asInt
            )
        }
        
        // 处理调色板
        val paletteArray = jsonObject.getAsJsonArray("color_palette")
        val colorPalette = paletteArray.map { paletteElement ->
            val paletteJson = paletteElement.asJsonObject
            com.example.coloringproject.data.ColorPalette(
                id = paletteJson.get("id").asInt,
                colorHex = paletteJson.get("color_hex").asString,
                colorRgb = paletteJson.get("color_rgb")?.asJsonArray?.map { it.asInt } ?: listOf(255, 255, 255),
                name = paletteJson.get("name").asString,
                usageCount = paletteJson.get("usage_count")?.asInt ?: 0
            )
        }
        
        // 处理元数据
        val metadata = jsonObject.getAsJsonObject("metadata")
        val imageSize = metadata.getAsJsonObject("image_size")

        return ColoringData(
            metadata = com.example.coloringproject.data.Metadata(
                version = metadata.get("version")?.asString ?: "2.0",
                sourceType = metadata.get("source_type")?.asString ?: "optimized",
                imageSize = com.example.coloringproject.data.ImageSize(
                    width = imageSize?.get("width")?.asInt ?: 800,
                    height = imageSize?.get("height")?.asInt ?: 600
                ),
                totalRegions = metadata.get("total_regions").asInt,
                totalColors = metadata.get("total_colors").asInt,
                difficulty = metadata.get("difficulty").asString,
                estimatedTimeMinutes = metadata.get("estimated_time_minutes").asInt
            ),
            regions = regions,
            colorPalette = colorPalette
        )
    }

    /**
     * 解压缩像素数据
     */
    private fun decompressPixels(compressed: com.google.gson.JsonObject): List<List<Int>> {
        val type = compressed.get("type").asString
        val pixels = mutableListOf<List<Int>>()
        
        when (type) {
            "empty" -> return emptyList()
            
            "rle" -> {
                // 游程编码解压
                val data = compressed.getAsJsonArray("data")
                data.forEach { segment ->
                    val seg = segment.asJsonArray
                    val y = seg[0].asInt
                    val xStart = seg[1].asInt
                    val length = seg[2].asInt
                    
                    for (i in 0 until length) {
                        pixels.add(listOf(xStart + i, y))
                    }
                }
            }
            
            "bbox" -> {
                // 边界框+掩码解压
                val bbox = compressed.getAsJsonArray("bbox")
                val mask = compressed.getAsJsonArray("mask")
                val minX = bbox[0].asInt
                val minY = bbox[1].asInt
                
                mask.forEachIndexed { yIdx, row ->
                    row.asJsonArray.forEachIndexed { xIdx, value ->
                        if (value.asInt == 1) {
                            pixels.add(listOf(minX + xIdx, minY + yIdx))
                        }
                    }
                }
            }
            
            else -> {
                Log.w("SimpleAssetManager", "Unknown compression type: $type")
            }
        }
        
        return pixels
    }

    /**
     * 加载线稿图片
     */
    suspend fun loadOutlineBitmap(imageFileName: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            Log.d("SimpleAssetManager", "Loading outline image: $imageFileName")
            val inputStream = context.assets.open(imageFileName)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            
            if (bitmap == null) {
                throw IllegalArgumentException("Failed to decode image: $imageFileName")
            }
            
            Log.d("SimpleAssetManager", "Successfully loaded outline image: ${bitmap.width}x${bitmap.height}")
            Result.success(bitmap)
        } catch (e: IOException) {
            Log.e("SimpleAssetManager", "Failed to load outline image: $imageFileName", e)
            Result.failure(e)
        }
    }

    /**
     * 加载区域图片
     */
    suspend fun loadRegionImage(imageFileName: String): Result<Bitmap> = withContext(Dispatchers.IO) {
        try {
            Log.d("SimpleAssetManager", "Loading region image: $imageFileName")
            val inputStream = context.assets.open(imageFileName)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            
            if (bitmap == null) {
                throw IllegalArgumentException("Failed to decode image: $imageFileName")
            }
            
            Log.d("SimpleAssetManager", "Successfully loaded region image: ${bitmap.width}x${bitmap.height}")
            Result.success(bitmap)
        } catch (e: IOException) {
            Log.e("SimpleAssetManager", "Failed to load region image: $imageFileName", e)
            Result.failure(e)
        }
    }

    /**
     * 检查资源文件是否存在
     */
    fun assetExists(fileName: String): Boolean {
        return try {
            context.assets.open(fileName).use { true }
        } catch (e: IOException) {
            false
        }
    }

    /**
     * 获取资源文件大小
     */
    fun getAssetSize(fileName: String): Long {
        return try {
            context.assets.openFd(fileName).use { it.length }
        } catch (e: IOException) {
            -1L
        }
    }

    /**
     * 获取可用项目列表
     */
    suspend fun getAvailableProjects(): Result<List<ColoringProject>> = withContext(Dispatchers.IO) {
        try {
            val projects = mutableListOf<ColoringProject>()

            // 简单的项目检测逻辑
            val assetList = context.assets.list("") ?: emptyArray()
            val jsonFiles = assetList.filter { it.endsWith(".json") }

            jsonFiles.forEach { jsonFile ->
                val baseName = jsonFile.removeSuffix(".json")
                val outlineFile = "${baseName}_outline.png"

                if (assetExists(outlineFile)) {
                    // 尝试加载JSON获取元数据
                    try {
                        val dataResult = loadColoringData(jsonFile)
                        if (dataResult.isSuccess) {
                            val data = dataResult.getOrNull()!!
                            projects.add(
                                ColoringProject(
                                    id = baseName,
                                    name = baseName.replace("_", " ").replaceFirstChar { it.uppercase() },
                                    jsonFile = jsonFile,
                                    outlineFile = outlineFile,
                                    difficulty = data.metadata.difficulty,
                                    totalRegions = data.metadata.totalRegions,
                                    totalColors = data.metadata.totalColors,
                                    estimatedTime = data.metadata.estimatedTimeMinutes
                                )
                            )
                        }
                    } catch (e: Exception) {
                        Log.w("SimpleAssetManager", "Failed to load project metadata for $jsonFile", e)
                    }
                }
            }

            Log.d("SimpleAssetManager", "Found ${projects.size} available projects")
            Result.success(projects)
        } catch (e: Exception) {
            Log.e("SimpleAssetManager", "Error getting available projects", e)
            Result.failure(e)
        }
    }

    /**
     * 转换超级压缩格式到ColoringData
     */
    private fun convertUltraCompressedToColoringData(jsonObject: com.google.gson.JsonObject): ColoringData {
        // 处理元数据
        val meta = jsonObject.getAsJsonObject("meta")
        val metadata = com.example.coloringproject.data.Metadata(
            version = meta.get("version")?.asString ?: meta.get("v")?.asString ?: "3.0",
            sourceType = "ultra_compressed",
            imageSize = com.example.coloringproject.data.ImageSize(
                width = meta.getAsJsonArray("size")[0].asInt,
                height = meta.getAsJsonArray("size")[1].asInt
            ),
            totalRegions = meta.get("regions").asInt,
            totalColors = meta.get("colors").asInt,
            difficulty = "medium", // 简化格式中可能没有难度信息
            estimatedTimeMinutes = 30
        )

        // 处理区域数据
        val regionsArray = jsonObject.getAsJsonArray("regions")
        val regions = regionsArray.map { regionElement ->
            val regionJson = regionElement.asJsonObject

            // 处理像素数据：支持pixels和contour两种格式
            val pixels = when {
                regionJson.has("pixels") -> {
                    // v2.0格式：使用pixels数据
                    decompressUltraCompressedPixels(regionJson.getAsJsonObject("pixels"))
                }
                regionJson.has("contour") -> {
                    // v3.0格式：将contour转换为pixels
                    convertContourToPixels(
                        regionJson.getAsJsonObject("contour"),
                        metadata.imageSize.width,
                        metadata.imageSize.height
                    )
                }
                else -> {
                    Log.w("SimpleAssetManager", "区域 ${regionJson.get("id").asInt} 既没有pixels也没有contour数据")
                    emptyList()
                }
            }

            com.example.coloringproject.data.Region(
                id = regionJson.get("id").asInt,
                clusterId = null,
                color = hexToRgb(regionJson.get("hex").asString),
                colorHex = regionJson.get("hex").asString,
                area = regionJson.get("original_pixel_count")?.asInt
                    ?: regionJson.get("count")?.asInt
                    ?: pixels.size,
                pixelCount = regionJson.get("original_pixel_count")?.asInt
                    ?: regionJson.get("count")?.asInt
                    ?: pixels.size,
                pixels = pixels,
                boundingBox = null,
                fillOrder = regionJson.get("order")?.asInt
            )
        }

        // 处理调色板
        val paletteArray = jsonObject.getAsJsonArray("palette")
        val colorPalette = paletteArray.mapIndexed { index, paletteElement ->
            val paletteJson = paletteElement.asJsonObject

            // 支持不同的颜色字段名
            val colorHex = paletteJson.get("color_hex")?.asString
                ?: paletteJson.get("hex")?.asString
                ?: "#000000"

            com.example.coloringproject.data.ColorPalette(
                id = paletteJson.get("id")?.asInt ?: (index + 1), // 从1开始编号
                colorHex = colorHex,
                colorRgb = hexToRgb(colorHex),
                name = paletteJson.get("name")?.asString ?: "颜色${index + 1}",
                usageCount = paletteJson.get("usage_count")?.asInt ?: 0
            )
        }

        return ColoringData(
            metadata = metadata,
            regions = regions,
            colorPalette = colorPalette
        )
    }

    /**
     * 将轮廓数据转换为像素数据
     */
    private fun convertContourToPixels(contour: com.google.gson.JsonObject, width: Int, height: Int): List<List<Int>> {
        try {
            val type = contour.get("type")?.asString ?: "relative"
            val dataArray = contour.getAsJsonArray("data")
            val isClosed = contour.get("is_closed")?.asBoolean ?: true

            if (dataArray.size() == 0) {
                Log.w("SimpleAssetManager", "轮廓数据为空")
                return emptyList()
            }

            // 解析轮廓点
            val contourPoints = mutableListOf<Pair<Int, Int>>()

            when (type) {
                "relative" -> {
                    // 相对坐标：第一个点是绝对坐标，后续是相对偏移
                    var currentX = 0
                    var currentY = 0

                    dataArray.forEachIndexed { index, pointElement ->
                        val pointArray = pointElement.asJsonArray
                        if (pointArray.size() >= 2) {
                            val deltaX = pointArray[0].asInt
                            val deltaY = pointArray[1].asInt

                            if (index == 0) {
                                // 第一个点是绝对坐标
                                currentX = deltaX
                                currentY = deltaY
                            } else {
                                // 后续点是相对偏移
                                currentX += deltaX
                                currentY += deltaY
                            }

                            // 确保坐标在图像范围内
                            val clampedX = currentX.coerceIn(0, width - 1)
                            val clampedY = currentY.coerceIn(0, height - 1)
                            contourPoints.add(Pair(clampedX, clampedY))
                        }
                    }
                }
                "absolute" -> {
                    // 绝对坐标
                    dataArray.forEach { pointElement ->
                        val pointArray = pointElement.asJsonArray
                        if (pointArray.size() >= 2) {
                            val x = pointArray[0].asInt.coerceIn(0, width - 1)
                            val y = pointArray[1].asInt.coerceIn(0, height - 1)
                            contourPoints.add(Pair(x, y))
                        }
                    }
                }
                else -> {
                    Log.w("SimpleAssetManager", "不支持的轮廓类型: $type")
                    return emptyList()
                }
            }

            if (contourPoints.isEmpty()) {
                Log.w("SimpleAssetManager", "解析轮廓点失败")
                return emptyList()
            }

            // 使用扫描线算法填充多边形
            val pixels = fillPolygon(contourPoints, width, height)
            Log.d("SimpleAssetManager", "轮廓转像素完成: ${contourPoints.size}个轮廓点 -> ${pixels.size}个像素")

            return pixels

        } catch (e: Exception) {
            Log.e("SimpleAssetManager", "轮廓转像素失败", e)
            return emptyList()
        }
    }

    /**
     * 使用扫描线算法填充多边形
     */
    private fun fillPolygon(contourPoints: List<Pair<Int, Int>>, width: Int, height: Int): List<List<Int>> {
        if (contourPoints.size < 3) {
            // 不足3个点无法构成多边形，返回轮廓点本身
            return contourPoints.map { listOf(it.first, it.second) }
        }

        val pixels = mutableListOf<List<Int>>()

        // 简化版扫描线算法：对每一行找交点
        for (y in 0 until height) {
            val intersections = mutableListOf<Int>()

            // 找到与当前扫描线的交点
            for (i in contourPoints.indices) {
                val p1 = contourPoints[i]
                val p2 = contourPoints[(i + 1) % contourPoints.size]

                val y1 = p1.second
                val y2 = p2.second

                // 检查线段是否与扫描线相交
                if ((y1 <= y && y < y2) || (y2 <= y && y < y1)) {
                    if (y1 != y2) {
                        // 计算交点的x坐标
                        val x = p1.first + (y - y1) * (p2.first - p1.first) / (y2 - y1)
                        if (x in 0 until width) {
                            intersections.add(x)
                        }
                    }
                }
            }

            // 对交点排序
            intersections.sort()

            // 填充交点之间的像素（奇偶规则）
            for (i in 0 until intersections.size step 2) {
                if (i + 1 < intersections.size) {
                    val startX = intersections[i]
                    val endX = intersections[i + 1]

                    for (x in startX..endX) {
                        if (x in 0 until width) {
                            pixels.add(listOf(x, y))
                        }
                    }
                }
            }
        }

        return pixels
    }

    /**
     * 解压缩超级压缩的像素数据
     */
    private fun decompressUltraCompressedPixels(compressed: com.google.gson.JsonObject): List<List<Int>> {
        val type = compressed.get("type").asString
        val pixels = mutableListOf<List<Int>>()

        when (type) {
            "empty" -> return emptyList()

            "delta" -> {
                // 差分编码解压
                val data = compressed.getAsJsonArray("data")
                var x = 0
                var y = 0

                data.forEach { deltaElement ->
                    val delta = deltaElement.asJsonArray
                    val dx = delta[0].asInt
                    val dy = delta[1].asInt

                    x += dx
                    y += dy
                    pixels.add(listOf(x, y))
                }
            }

            "polygon" -> {
                // 多边形近似解压
                if (compressed.has("data")) {
                    // 如果有原始数据，直接使用
                    val data = compressed.getAsJsonArray("data")
                    data.forEach { pixelElement ->
                        val coords = pixelElement.asJsonArray
                        pixels.add(listOf(coords[0].asInt, coords[1].asInt))
                    }
                } else if (compressed.has("boundary")) {
                    // 如果只有边界点，只使用边界点（避免错误填充）
                    val boundary = compressed.getAsJsonArray("boundary")
                    boundary.forEach { point ->
                        val coords = point.asJsonArray
                        pixels.add(listOf(coords[0].asInt, coords[1].asInt))
                    }
                    Log.w("SimpleAssetManager", "多边形区域只使用边界点，可能不完整")
                }
            }

            "rectangle" -> {
                // 矩形压缩解压
                val rect = compressed.getAsJsonArray("rect")
                val x = rect[0].asInt
                val y = rect[1].asInt
                val width = rect[2].asInt
                val height = rect[3].asInt

                for (py in y until y + height) {
                    for (px in x until x + width) {
                        pixels.add(listOf(px, py))
                    }
                }
            }

            "rect_minus" -> {
                // 矩形减法解压
                val rect = compressed.getAsJsonArray("rect")
                val missing = compressed.getAsJsonArray("missing")
                val missingSet = missing.map { point ->
                    val coords = point.asJsonArray
                    Pair(coords[0].asInt, coords[1].asInt)
                }.toSet()

                val x = rect[0].asInt
                val y = rect[1].asInt
                val width = rect[2].asInt
                val height = rect[3].asInt

                for (py in y until y + height) {
                    for (px in x until x + width) {
                        if (!missingSet.contains(Pair(px, py))) {
                            pixels.add(listOf(px, py))
                        }
                    }
                }
            }

            "pattern" -> {
                // 模式压缩解压（回退到原始数据）
                val data = compressed.getAsJsonArray("data")
                data.forEach { pixelElement ->
                    val coords = pixelElement.asJsonArray
                    pixels.add(listOf(coords[0].asInt, coords[1].asInt))
                }
            }

            // 回退到基础压缩格式
            else -> {
                pixels.addAll(decompressPixels(compressed))
            }
        }

        return pixels
    }

    /**
     * 十六进制颜色转RGB
     */
    private fun hexToRgb(hex: String): List<Int> {
        val cleanHex = hex.removePrefix("#")
        return listOf(
            cleanHex.substring(0, 2).toInt(16),
            cleanHex.substring(2, 4).toInt(16),
            cleanHex.substring(4, 6).toInt(16)
        )
    }
}

/**
 * 填色项目信息
 */
data class ColoringProject(
    val id: String,
    val name: String,
    val jsonFile: String,
    val outlineFile: String,
    val difficulty: String,
    val totalRegions: Int,
    val totalColors: Int,
    val estimatedTime: Int
) {
    val difficultyLevel: Int
        get() = when (difficulty) {
            "easy" -> 1
            "medium" -> 2
            "hard" -> 3
            else -> 2
        }
}

/**
 * 资源加载状态
 */
sealed class LoadingState<out T> {
    object Loading : LoadingState<Nothing>()
    data class Success<T>(val data: T) : LoadingState<T>()
    data class Error(val exception: Throwable) : LoadingState<Nothing>()
}

/**
 * 扩展函数：将Result转换为LoadingState
 */
fun <T> Result<T>.toLoadingState(): LoadingState<T> {
    return fold(
        onSuccess = { LoadingState.Success(it) },
        onFailure = { LoadingState.Error(it) }
    )
}
